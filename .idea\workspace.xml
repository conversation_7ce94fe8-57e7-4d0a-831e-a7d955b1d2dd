<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="da829768-5df5-4017-b7ed-9b43f1bd6311" name="Changes" comment="feat: small update to readme" />
    <list id="4c8177e0-3a1d-4393-833d-45a979f323da" name="local" comment="">
      <change afterPath="$PROJECT_DIR$/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/backend-next.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/inspectionProfiles/Project_Default.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/inspectionProfiles/profiles_settings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zdBn0aeHU7KItFFjVbQTCCu1Hb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python tests.Python tests for test_api_endpoints.TestAsyncTestEndpoints.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_api_endpoints.TestConcurrentRequests.test_mixed_endpoint_concurrency.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_api_endpoints.TestErrorHandling.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_api_endpoints.TestHomeEndpoints.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_api_endpoints.TestUserEndpoints.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_api_endpoints.TestVesselEndpoints.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_auth.TestAuthHandler.test_call_with_expired_token.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_auth.TestAuthHandler.test_call_with_invalid_token.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_auth.TestAuthHandler.test_call_with_missing_token.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_auth.TestAuthHandler.test_call_with_valid_token.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_auth.TestAuthHandler.test_init_with_empty_secret.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_auth.TestAuthHandler.test_init_with_valid_secret.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_auth.TestManagedOwners.test_managed_owners_empty_response.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_auth.TestManagedOwners.test_managed_owners_no_value_key.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_auth.TestManagedOwners.test_managed_owners_success.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_auth.TestOwnerVatCheck.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_auth.TestValidateOwnerAccess.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_context.TestGetDependencies.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_context_utils.TestExtractKwargs.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_context_utils.TestRenameOwnerVat.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_context_utils.TestRenameVat.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_context_utils.TestSelectKeys.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_context_utils.TestUtilityFunctionsIntegration.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_home_logic.TestHomeLogic.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_home_service.TestHomeService.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_module_interactions.TestHomeModuleWorkflow.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_redis_client.TestRedisApi.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_redis_client.TestRedisApi.test_get_key_data_error.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_redis_client.TestRedisApi.test_get_key_data_success.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_redis_client.TestRedisApi.test_get_key_data_with_format.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_redis_client.TestRedisApi.test_redis_api_singleton.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_redis_client.TestRedisApi.test_retrieve_time_series_data_error.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_redis_client.TestRedisApi.test_retrieve_time_series_data_no_request.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_redis_client.TestRedisApi.test_retrieve_time_series_data_success.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_redis_client.TestRedisApiIntegration.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_redis_client.TestRedisApiMocking.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_redis_integration.TestRedisClientIntegration.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests for test_settings.TestSettings.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests in test_api_endpoints.py.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests in test_concurrent_requests.py.executor&quot;: &quot;Debug&quot;,
    &quot;Python.ddos_load_test.executor&quot;: &quot;Run&quot;,
    &quot;Python.manual_test_commands.executor&quot;: &quot;Run&quot;,
    &quot;Python.router.executor&quot;: &quot;Run&quot;,
    &quot;Python.run fastapi uvicorn.executor&quot;: &quot;Run&quot;,
    &quot;Python.run.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_concurrent_requests.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_config.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;da/refactoring&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/projects/backend-next&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\modules\export" />
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\general_logic\helpers" />
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\vessel" />
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\config" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\test" />
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\config" />
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\general_logic_helper\utils" />
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\general_logic_helper\config" />
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\general_logic_helper\helpers\excel" />
    </key>
  </component>
  <component name="RunManager" selected="Python.run">
    <configuration name="run fastapi uvicorn" type="PythonConfigurationType" factoryName="Python">
      <module name="backend-next" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="Python 3.11 (backend-next)" />
      <option name="WORKING_DIRECTORY" value="C:\Users\<USER>\Desktop\projects\backend-next" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="uvicorn" />
      <option name="PARAMETERS" value="run:app --host 0.0.0.0 --port 8769 --reload" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="true" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="run" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="backend-next" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="Python 3.11 (backend-next)" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/run.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="Python tests for test_api_endpoints.TestConcurrentRequests.test_mixed_endpoint_concurrency" type="tests" factoryName="Autodetect" temporary="true" nameIsGenerated="true">
      <module name="backend-next" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/test/integration" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;test_api_endpoints.TestConcurrentRequests.test_mixed_endpoint_concurrency&quot;" />
      <option name="_new_targetType" value="&quot;PYTHON&quot;" />
      <method v="2" />
    </configuration>
    <configuration name="Python tests for test_api_endpoints.TestErrorHandling" type="tests" factoryName="Autodetect" temporary="true" nameIsGenerated="true">
      <module name="backend-next" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/test/integration" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;test_api_endpoints.TestErrorHandling&quot;" />
      <option name="_new_targetType" value="&quot;PYTHON&quot;" />
      <method v="2" />
    </configuration>
    <configuration name="Python tests for test_module_interactions.TestHomeModuleWorkflow" type="tests" factoryName="Autodetect" temporary="true" nameIsGenerated="true">
      <module name="backend-next" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/test/integration" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;test_module_interactions.TestHomeModuleWorkflow&quot;" />
      <option name="_new_targetType" value="&quot;PYTHON&quot;" />
      <method v="2" />
    </configuration>
    <configuration name="Python tests for test_redis_client.TestRedisApi.test_redis_api_singleton" type="tests" factoryName="Autodetect" temporary="true" nameIsGenerated="true">
      <module name="backend-next" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/test/unit" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;test_redis_client.TestRedisApi.test_redis_api_singleton&quot;" />
      <option name="_new_targetType" value="&quot;PYTHON&quot;" />
      <method v="2" />
    </configuration>
    <configuration name="Python tests for test_redis_integration.TestRedisClientIntegration" type="tests" factoryName="Autodetect" temporary="true" nameIsGenerated="true">
      <module name="backend-next" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/test/integration" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;test_redis_integration.TestRedisClientIntegration&quot;" />
      <option name="_new_targetType" value="&quot;PYTHON&quot;" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.run fastapi uvicorn" />
      <item itemvalue="Python.run" />
      <item itemvalue="Python tests.Python tests for test_redis_client.TestRedisApi.test_redis_api_singleton" />
      <item itemvalue="Python tests.Python tests for test_api_endpoints.TestConcurrentRequests.test_mixed_endpoint_concurrency" />
      <item itemvalue="Python tests.Python tests for test_api_endpoints.TestErrorHandling" />
      <item itemvalue="Python tests.Python tests for test_module_interactions.TestHomeModuleWorkflow" />
      <item itemvalue="Python tests.Python tests for test_redis_integration.TestRedisClientIntegration" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python tests.Python tests for test_redis_client.TestRedisApi.test_redis_api_singleton" />
        <item itemvalue="Python tests.Python tests for test_redis_integration.TestRedisClientIntegration" />
        <item itemvalue="Python tests.Python tests for test_module_interactions.TestHomeModuleWorkflow" />
        <item itemvalue="Python tests.Python tests for test_api_endpoints.TestConcurrentRequests.test_mixed_endpoint_concurrency" />
        <item itemvalue="Python tests.Python tests for test_api_endpoints.TestErrorHandling" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.19416.19" />
        <option value="bundled-python-sdk-337b0a7a993a-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.19416.19" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="da829768-5df5-4017-b7ed-9b43f1bd6311" name="Changes" comment="" />
      <changelist id="4c8177e0-3a1d-4393-833d-45a979f323da" name="local" comment="" />
      <created>1752051232428</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752051232428</updated>
      <workItem from="1752051233476" duration="14834000" />
      <workItem from="1752128789726" duration="22998000" />
      <workItem from="1752215157284" duration="14887000" />
      <workItem from="1752476439586" duration="18220000" />
      <workItem from="1752560617911" duration="22636000" />
      <workItem from="1752735317003" duration="16919000" />
      <workItem from="1752825168982" duration="9208000" />
      <workItem from="1753079438382" duration="20419000" />
      <workItem from="1753165146807" duration="23548000" />
      <workItem from="1753255542875" duration="21607000" />
      <workItem from="1753337543803" duration="31385000" />
      <workItem from="1753383556117" duration="34522000" />
      <workItem from="1753683941725" duration="3287000" />
      <workItem from="1753689449012" duration="20421000" />
      <workItem from="1753769842271" duration="1696000" />
    </task>
    <task id="LOCAL-00018" summary="fix: fix base redis client json and data handling in request">
      <option name="closed" value="true" />
      <created>1753188825434</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1753188825434</updated>
    </task>
    <task id="LOCAL-00019" summary="fix: Change call to redis client for calculation helpers">
      <option name="closed" value="true" />
      <created>1753188946850</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1753188946850</updated>
    </task>
    <task id="LOCAL-00020" summary="fix: fix time series method to work with home page and data analytics">
      <option name="closed" value="true" />
      <created>1753188999835</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1753188999835</updated>
    </task>
    <task id="LOCAL-00021" summary="feat: get dependencies without service class for calculation modules">
      <option name="closed" value="true" />
      <created>1753189056780</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1753189056780</updated>
    </task>
    <task id="LOCAL-00022" summary="fix: minor changes and fixes in user module">
      <option name="closed" value="true" />
      <created>1753189153776</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1753189153776</updated>
    </task>
    <task id="LOCAL-00023" summary="fix: move dependency in efficiency module">
      <option name="closed" value="true" />
      <created>1753189222959</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1753189222959</updated>
    </task>
    <task id="LOCAL-00024" summary="fix: move dependency in vessel module">
      <option name="closed" value="true" />
      <created>1753189251866</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1753189251866</updated>
    </task>
    <task id="LOCAL-00025" summary="fix: move dependency in home module">
      <option name="closed" value="true" />
      <created>1753189295783</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1753189295783</updated>
    </task>
    <task id="LOCAL-00026" summary="feat: cii module">
      <option name="closed" value="true" />
      <created>1753189358199</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1753189358199</updated>
    </task>
    <task id="LOCAL-00027" summary="feat: mrv module">
      <option name="closed" value="true" />
      <created>1753189387290</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1753189387290</updated>
    </task>
    <task id="LOCAL-00028" summary="feat: data analytics module">
      <option name="closed" value="true" />
      <created>1753189407141</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1753189407141</updated>
    </task>
    <task id="LOCAL-00029" summary="feat: add routers to fastapi">
      <option name="closed" value="true" />
      <created>1753189446197</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1753189446197</updated>
    </task>
    <task id="LOCAL-00030" summary="feat: new ways to get params">
      <option name="closed" value="true" />
      <created>1753276917408</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1753276917408</updated>
    </task>
    <task id="LOCAL-00031" summary="feat: new ways to get params, modules update">
      <option name="closed" value="true" />
      <created>1753277059336</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1753277059336</updated>
    </task>
    <task id="LOCAL-00032" summary="feat: export module">
      <option name="closed" value="true" />
      <created>1753277121608</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1753277121608</updated>
    </task>
    <task id="LOCAL-00033" summary="feat: engine health module , we will test and use it later on">
      <option name="closed" value="true" />
      <created>1753277143796</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1753277143796</updated>
    </task>
    <task id="LOCAL-00034" summary="fix: rename redis service to redis api">
      <option name="closed" value="true" />
      <created>1753278744509</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1753278744509</updated>
    </task>
    <task id="LOCAL-00035" summary="feat: break up dependencies into logical units">
      <option name="closed" value="true" />
      <created>1753369552197</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1753369552197</updated>
    </task>
    <task id="LOCAL-00036" summary="feat: break up dependencies into logical units">
      <option name="closed" value="true" />
      <created>1753369564024</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1753369564024</updated>
    </task>
    <task id="LOCAL-00037" summary="feat: use config for fastapi and uvicorn">
      <option name="closed" value="true" />
      <created>1753369637675</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1753369637675</updated>
    </task>
    <task id="LOCAL-00038" summary="feat: config for redis client">
      <option name="closed" value="true" />
      <created>1753369672338</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1753369672338</updated>
    </task>
    <task id="LOCAL-00039" summary="fix: error handling improvements">
      <option name="closed" value="true" />
      <created>1753369698949</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1753369698949</updated>
    </task>
    <task id="LOCAL-00040" summary="fix: move file to general logic for now">
      <option name="closed" value="true" />
      <created>1753369712346</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1753369712346</updated>
    </task>
    <task id="LOCAL-00041" summary="feat: change router's and logic's parameter handling to use the general way of just passing on the parameters">
      <option name="closed" value="true" />
      <created>1753369811359</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1753369811359</updated>
    </task>
    <task id="LOCAL-00042" summary="fix: rename everything">
      <option name="closed" value="true" />
      <created>1753384811315</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1753384811315</updated>
    </task>
    <task id="LOCAL-00043" summary="fix: move helpers around">
      <option name="closed" value="true" />
      <created>1753453724953</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1753453724953</updated>
    </task>
    <task id="LOCAL-00044" summary="fix: context files">
      <option name="closed" value="true" />
      <created>1753453826801</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1753453826801</updated>
    </task>
    <task id="LOCAL-00045" summary="fix: move helper files around">
      <option name="closed" value="true" />
      <created>1753453843679</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1753453843679</updated>
    </task>
    <task id="LOCAL-00046" summary="fix: change in context">
      <option name="closed" value="true" />
      <created>1753453866102</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1753453866102</updated>
    </task>
    <task id="LOCAL-00047" summary="fix: add commenting to error handlers">
      <option name="closed" value="true" />
      <created>1753453893076</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1753453893076</updated>
    </task>
    <task id="LOCAL-00048" summary="fix: redis api client comments">
      <option name="closed" value="true" />
      <created>1753453925814</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1753453925814</updated>
    </task>
    <task id="LOCAL-00049" summary="fix: change routes to use context the right way">
      <option name="closed" value="true" />
      <created>1753453985769</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1753453985769</updated>
    </task>
    <task id="LOCAL-00050" summary="fix: update requirements">
      <option name="closed" value="true" />
      <created>1753454023040</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1753454023040</updated>
    </task>
    <task id="LOCAL-00051" summary="fix: update run">
      <option name="closed" value="true" />
      <created>1753454031705</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1753454031705</updated>
    </task>
    <task id="LOCAL-00052" summary="feat: readme">
      <option name="closed" value="true" />
      <created>1753454040198</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1753454040198</updated>
    </task>
    <task id="LOCAL-00053" summary="feat: generated test cases we have for now">
      <option name="closed" value="true" />
      <created>1753454187414</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1753454187414</updated>
    </task>
    <task id="LOCAL-00054" summary="fix: delete yml">
      <option name="closed" value="true" />
      <created>1753454265625</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1753454265625</updated>
    </task>
    <task id="LOCAL-00055" summary="fix: delete yml">
      <option name="closed" value="true" />
      <created>1753454323067</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1753454323067</updated>
    </task>
    <task id="LOCAL-00056" summary="fix: added minor fix">
      <option name="closed" value="true" />
      <created>1753685408720</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1753685408720</updated>
    </task>
    <task id="LOCAL-00057" summary="fix: move files into test folder">
      <option name="closed" value="true" />
      <created>1753686014219</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1753686014219</updated>
    </task>
    <task id="LOCAL-00058" summary="fix: cii reset week and reset all">
      <option name="closed" value="true" />
      <created>1753695035553</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1753695035554</updated>
    </task>
    <task id="LOCAL-00059" summary="fix: cii update">
      <option name="closed" value="true" />
      <created>1753696064124</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1753696064124</updated>
    </task>
    <task id="LOCAL-00060" summary="fix: tenant vat header name from env">
      <option name="closed" value="true" />
      <created>1753698280249</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1753698280249</updated>
    </task>
    <task id="LOCAL-00061" summary="feat: env example">
      <option name="closed" value="true" />
      <created>1753698317959</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1753698317959</updated>
    </task>
    <task id="LOCAL-00062" summary="fix: change naming to not confuse the IDE">
      <option name="closed" value="true" />
      <created>1753699981774</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1753699981774</updated>
    </task>
    <task id="LOCAL-00063" summary="fix: more error handlers">
      <option name="closed" value="true" />
      <created>1753706307028</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1753706307028</updated>
    </task>
    <task id="LOCAL-00064" summary="feat: add comments to modules">
      <option name="closed" value="true" />
      <created>1753708190246</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1753708190246</updated>
    </task>
    <task id="LOCAL-00065" summary="feat: update readme with good to know section">
      <option name="closed" value="true" />
      <created>1753708467813</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1753708467813</updated>
    </task>
    <task id="LOCAL-00066" summary="feat: small update to readme">
      <option name="closed" value="true" />
      <created>1753708689030</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1753708689030</updated>
    </task>
    <option name="localTasksCounter" value="67" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat: change router's and logic's parameter handling to use the general way of just passing on the parameters" />
    <MESSAGE value="fix: rename everything" />
    <MESSAGE value="fix: move helpers around" />
    <MESSAGE value="fix: context files" />
    <MESSAGE value="fix: move helper files around" />
    <MESSAGE value="fix: change in context" />
    <MESSAGE value="fix: add commenting to error handlers" />
    <MESSAGE value="fix: redis api client comments" />
    <MESSAGE value="fix: change routes to use context the right way" />
    <MESSAGE value="fix: update requirements" />
    <MESSAGE value="fix: update run" />
    <MESSAGE value="feat: readme" />
    <MESSAGE value="feat: generated test cases we have for now" />
    <MESSAGE value="fix: delete yml" />
    <MESSAGE value="fix: added minor fix" />
    <MESSAGE value="fix: move files into test folder" />
    <MESSAGE value="fix: cii reset week and reset all" />
    <MESSAGE value="fix: cii update" />
    <MESSAGE value="fix: tenant vat header name from env" />
    <MESSAGE value="feat: env example" />
    <MESSAGE value="fix: change naming to not confuse the IDE" />
    <MESSAGE value="fix: more error handlers" />
    <MESSAGE value="feat: add comments to modules" />
    <MESSAGE value="feat: update readme with good to know section" />
    <MESSAGE value="feat: small update to readme" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: small update to readme" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/app/modules/export/logic_helpers/generate_file.py</url>
          <line>280</line>
          <option name="timeStamp" value="58" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/app/modules/efficiency/efficiency_logic.py</url>
          <line>16</line>
          <option name="timeStamp" value="99" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/app/modules/anomaly_detection/anomaly_detection_router.py</url>
          <line>25</line>
          <option name="timeStamp" value="104" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/test/unit/test_redis_client.py</url>
          <line>70</line>
          <option name="timeStamp" value="118" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/app/modules/cii/logic_helpers/cii.py</url>
          <line>295</line>
          <option name="timeStamp" value="124" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/app/modules/cii/logic_helpers/cii.py</url>
          <line>261</line>
          <option name="timeStamp" value="125" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/test/async_test_scripts/test_concurrent_requests.py</url>
          <line>246</line>
          <option name="timeStamp" value="126" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/backend_next$run_fastapi_uvicorn.coverage" NAME="run fastapi uvicorn Coverage Results" MODIFIED="1753346193055" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="C:\Users\<USER>\Desktop\projects\backend-next" />
    <SUITE FILE_PATH="coverage/backend_next$router.coverage" NAME="router Coverage Results" MODIFIED="1753346545330" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/app/modules/mrv" />
    <SUITE FILE_PATH="coverage/backend_next$ddos_load_test.coverage" NAME="ddos_load_test Coverage Results" MODIFIED="1753709889912" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test/async_test_scripts" />
    <SUITE FILE_PATH="coverage/backend_next$run.coverage" NAME="run Coverage Results" MODIFIED="1753769872731" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="" />
    <SUITE FILE_PATH="coverage/backend_next$manual_test_commands.coverage" NAME="manual_test_commands Coverage Results" MODIFIED="1753709493637" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test/async_test_scripts" />
    <SUITE FILE_PATH="coverage/backend_next$.coverage" NAME=" Coverage Results" MODIFIED="1753710453305" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test/unit" />
    <SUITE FILE_PATH="coverage/backend_next$test_config.coverage" NAME="test_config Coverage Results" MODIFIED="1753448685865" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test/async_test_scripts" />
    <SUITE FILE_PATH="coverage/backend_next$test_concurrent_requests.coverage" NAME="test_concurrent_requests Coverage Results" MODIFIED="1753449421928" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test/async_test_scripts" />
  </component>
</project>